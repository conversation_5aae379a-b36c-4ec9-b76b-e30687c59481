import { gql } from '../../utils'

/**
 * 获取订单详情
 */
export const GET_ORDER_DETAIL = gql`
  query getOrderDetail($filter: CustomerOrdersFilterInput) {
    customer {
      orders(filter: $filter, currentPage: 1, pageSize: 1) {
        items {
          id
          increment_id
          encrypt {
            nid
            oid
          }
          order_date
          status
          status_tab {
            is_arrived
            status
            time
          }
          status_tab_label
          status_code
          number
          created_at
          grand_total
          payment_time_out
          ncoin_amount
          ncoin_used
          ncoins_amount
          quantity_ordered
          pickup_phone
          remark_comment
          carrier
          can_requisition
          display_shipping_address
          requisition_all
          requisition {
            reason
            status
            status_label
          }
          delivery_method {
            method
            method_label
          }
          can_cancel_requisition
          pickup_info {
            code
            qr_code
            status
            status_label
            expired_info
            start_date
          }
          payment_methods {
            logo
            name
            type
          }
          paid_time
          shipping_address {
            address_id
            firstname
            lastname
            middlename
            district
            region
            city
            street
            telephone
          }
          store_info {
            status
            business_hours
            closing_hours
            latitude
            longitude
            store_address
            store_id
            store_name
            telephone
          }
          car_vin {
            device_image
            device_name
            snNo
          }
          shipment_tracking {
            track_number
            carrier_code
            carrier_title
            state
            products {
              product_image
              product_name
              product_sku
              quantity_shipped
              product {
                name
                image {
                  url
                  label
                }
              }
            }
          }
          items {
            id
            product_sku
            product_type
            quantity_ordered
            status
            store_info {
              store_name
            }
            config_selected_options {
              value_label
            }
            requisition_status
            requisition_status_label
            coupons {
              item_count
              waiting_use_count
              expired_at
              status
              status_label
            }
            vouchers {
              item_count
              waiting_use_count
              expired_at
              status
              status_label
              items {
                name
                code
                qr_code
                expired_at
                status
                status_label
              }
            }
            is_ncoin_pay
            third_platform_member_code {
              item_count
              waiting_use_count
              expired_at
              status
              status_label
              items {
                name
                code
                expired_at
                status
                status_label
              }
            }
            product_sale_price {
              currency
              value
            }
            product {
              name
              description {
                html
              }
              image {
                url
                label
              }
              price_range {
                maximum_price {
                  discount {
                    amount_off
                  }
                  final_price {
                    currency
                    value
                  }
                  regular_price {
                    currency
                    value
                  }
                }
              }
            }
            digital_info {
              img_url
              label
              language {
                language
                title
              }
              resource_code
              resource_type
              sn_code
              status
              video_url
            }
            use_from_date
            use_to_date
          }
          ncoin_pay {
            grand_total
            subtotal
          }
          total {
            base_grand_total {
              value
              currency
            }
            subtotal {
              value
              currency
            }
            grand_total {
              value
              currency
            }
            total_shipping {
              value
              currency
            }
            discounts {
              label
              amount {
                value
                currency
              }
              distribute_ncoin
              distribute_amount
            }
          }
          is_migration
          migration_data {
            address
            create_date
            encrypt {
              nid
              oid
            }
            id
            items {
              product_material_code
              product_need_npoint
              product_qty
              product_thumb_img
              product_title
            }
            name
            need_ncoin
            order_num
            status
            status_code
            tel
          }
          invoice_info {
            can_invoice
            email
            phone
            tax_id
            title
            type
            zlink
          }
        }
      }
    }
  }
`
