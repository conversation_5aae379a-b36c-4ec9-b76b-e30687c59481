'use client'

import { useEffect, useRef } from 'react'
import { setupListeners } from '@reduxjs/toolkit/query'
import { Provider } from 'react-redux'

import { KEY_TOKEN, QUID_KEY } from '../../constants'
import { GLOBAL_INITIAL_STATE, TUserSliceState, USER_INITIAL_STATE } from '../../store'
import makeStore, { AppStore, RootState } from '../../store/store'
import { TBaseComponentProps } from '../../typings'
import { appCookie, isServer } from '../../utils'
import { defaultCookieOptions } from '../../utils/cookie'

type TStoreProviderProps = TBaseComponentProps & {
  quid: string
}

/**
 * Store provider
 */
const StoreProvider = ({ children, quid }: TStoreProviderProps) => {
  const storeRef = useRef<AppStore | null>(null)

  if (!storeRef.current) {
    const preloadedState: Partial<RootState> = {}

    if (!isServer) {
      // 导入初始化状态
      // 从 cookie 获取 token
      const authToken = appCookie.getItemSync<TUserSliceState['authToken']>(KEY_TOKEN)

      // 保存 token
      if (authToken) {
        preloadedState['user'] = {
          ...USER_INITIAL_STATE,
          isAuthLoading: true,
          authToken: authToken || null,
          isLoggedIn: !!authToken,
        }
      }

      // 保存 quid
      if (quid) {
        preloadedState['global'] = {
          ...GLOBAL_INITIAL_STATE,
          quid,
        }
        // 将服务端生成的 quid 保存到 cookies 中
        appCookie.setItem(QUID_KEY, quid, {
          ...defaultCookieOptions,
          expires: new Date(Date.now() + 1000 * 60 * 60 * 24), // 1 天
        })
      }
    }

    // Create the store instance the first time this renders
    storeRef.current = makeStore(preloadedState)
  }

  useEffect(() => {
    if (storeRef.current != null) {
      // configure listeners using the provided defaults
      // optional, but required for `refetchOnFocus`/`refetchOnReconnect` behaviors
      const unsubscribe = setupListeners(storeRef.current.dispatch)
      return unsubscribe
    }
  }, [])

  return <Provider store={storeRef.current}>{children}</Provider>
}

export default StoreProvider
