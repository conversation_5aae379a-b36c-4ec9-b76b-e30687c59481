'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  resolveCatchMessage,
  TCatchMessage,
  useLazyGetProvinceCityCountyQuery,
  useToastContext,
} from '@ninebot/core'
import type { TabsProps } from 'antd'
import { Tabs } from 'antd'
import clsx from 'clsx'

import { Skeleton } from '@/components'

import { performanceMonitor } from './PerformanceMonitor'

// ==================== 类型定义 ====================

/**
 * 地区数据项接口
 */
interface AreaItem {
  label: string
  id: string
}

/**
 * API返回数据类型
 */
interface ApiAreaItem {
  default_name: string
  region_id?: string
  city_id?: string
  district_id?: string
}

/**
 * 组件属性接口
 */
interface AddressSelectorProps {
  defaultValue?: {
    province?: string
    city?: string
    district?: string
  }
  onSelect?: (province: AreaItem, city: AreaItem, district: AreaItem) => void
  initialTab?: TabType // 初始显示的tab层级
}

/**
 * 地址类型
 */
type AddressType = 'region' | 'city'

/**
 * Tab类型枚举
 */
enum TabType {
  PROVINCE = 'province',
  CITY = 'city',
  DISTRICT = 'district',
}

// ==================== 常量定义 ====================

/**
 * 默认空值
 */
const DEFAULT_AREA_ITEM: AreaItem = { label: '', id: '' }

/**
 * 骨架屏配置
 */
const SKELETON_CONFIG = {
  listItemCount: 8,
  backgroundColor: '#F3F3F4',
  borderRadius: 8,
} as const

// ==================== 组件定义 ====================

// 简易内存缓存（会话级）
const areaCache = new Map<string, AreaItem[]>()

function getAreaCacheKey(type: AddressType, id?: string) {
  return `areaCache:${type}:${id || 'root'}`
}

function readAreaFromSession(type: AddressType, id?: string): AreaItem[] | null {
  if (typeof window === 'undefined') return null
  try {
    const key = getAreaCacheKey(type, id)
    const raw = sessionStorage.getItem(key)
    if (!raw) return null
    const parsed = JSON.parse(raw) as AreaItem[]
    return Array.isArray(parsed) ? parsed : null
  } catch (e) {
    console.error('Failed to read area data from sessionStorage:', e)
    return null
  }
}

function writeAreaToSession(type: AddressType, id: string | undefined, list: AreaItem[]) {
  if (typeof window === 'undefined') return
  try {
    const key = getAreaCacheKey(type, id)
    sessionStorage.setItem(key, JSON.stringify(list))
  } catch (e) {
    console.error('Failed to write area data to sessionStorage:', e)
    // 忽略存储异常
  }
}

/**
 * 渲染地址选项骨架屏
 */
const AddressItemSkeleton = () => (
  <Skeleton
    style={{
      width: '100%',
      height: '30px',
      borderRadius: SKELETON_CONFIG.borderRadius,
      backgroundColor: SKELETON_CONFIG.backgroundColor,
    }}
  />
)

/**
 * 加载骨架屏组件
 */
const LoadingSkeleton = () => (
  <div className="max-h-[320px] space-y-4 overflow-hidden">
    {Array.from({ length: SKELETON_CONFIG.listItemCount }, (_, index) => (
      <AddressItemSkeleton key={index} />
    ))}
  </div>
)

// ==================== 主组件 ====================

/**
 * 地址选择器组件
 *
 * 功能：
 * 1. 支持省市区三级联动选择
 * 2. 自动加载和缓存地址数据
 * 3. 支持默认值设置
 * 4. 提供友好的加载状态
 */
const AddressSelector = ({ defaultValue, onSelect, initialTab }: AddressSelectorProps) => {
  const getI18nString = useTranslations('Common')
  const [getAddressData, { isFetching }] = useLazyGetProvinceCityCountyQuery()
  const toast = useToastContext()

  // ==================== 状态管理 ====================

  // Tab状态
  const [activeTab, setActiveTab] = useState<TabType>(initialTab || TabType.PROVINCE)

  // 选中状态
  const [activeProvince, setActiveProvince] = useState<AreaItem>(DEFAULT_AREA_ITEM)
  const [activeCity, setActiveCity] = useState<AreaItem>(DEFAULT_AREA_ITEM)
  const [activeDistrict, setActiveDistrict] = useState<AreaItem>(DEFAULT_AREA_ITEM)

  // 数据状态
  const [provinces, setProvinces] = useState<AreaItem[]>([])
  const [cities, setCities] = useState<AreaItem[]>([])
  const [districts, setDistricts] = useState<AreaItem[]>([])

  // 控制状态
  const [isDefaultSet, setIsDefaultSet] = useState(true) // 是否设置默认值

  // ==================== 计算属性 ====================

  /**
   * 当前地址列表
   */
  const addressList = useMemo(() => {
    switch (activeTab) {
      case TabType.PROVINCE:
        return provinces
      case TabType.CITY:
        return cities
      case TabType.DISTRICT:
        return districts
      default:
        return []
    }
  }, [activeTab, provinces, cities, districts])

  /**
   * 当前选中的地址项
   */
  const currentAddress = useMemo(() => {
    switch (activeTab) {
      case TabType.PROVINCE:
        return activeProvince
      case TabType.CITY:
        return activeCity
      case TabType.DISTRICT:
        return activeDistrict
      default:
        return DEFAULT_AREA_ITEM
    }
  }, [activeTab, activeProvince, activeCity, activeDistrict])

  // ==================== 工具函数 ====================

  /**
   * 格式化API数据为组件数据
   */
  const formatApiData = useCallback(
    (data: ApiAreaItem[], type: AddressType, isCity: boolean = false): AreaItem[] => {
      return data.map((item) => {
        let id = ''
        if (type === 'city') {
          // 获取区县数据时使用district_id
          id = item.district_id || ''
        } else if (type === 'region') {
          if (isCity) {
            // 获取城市数据时使用city_id
            id = item.city_id || ''
          } else {
            // 获取省份数据时使用region_id
            id = item.region_id || ''
          }
        }
        return {
          id,
          label: item.default_name,
        }
      })
    },
    [],
  )

  /**
   * 获取地址数据
   */
  const fetchAddressData = useCallback(
    async ({ id, type }: { id?: string; type: AddressType }) => {
      const key = getAreaCacheKey(type, id)
      const operationName = `地址选择器-获取${type === 'region' ? (id ? '城市' : '省份') : '区县'}数据`

      // 读取内存缓存
      const memCached = areaCache.get(key)
      if (memCached && memCached.length) {
        performanceMonitor.record(`${operationName}-命中内存缓存`, { 数量: memCached.length })
        if (id && type === 'city') setDistricts(memCached)
        else if (id && type === 'region') setCities(memCached)
        else setProvinces(memCached)
        return
      }

      // 读取 sessionStorage
      const sessionCached = readAreaFromSession(type, id)
      if (sessionCached && sessionCached.length) {
        performanceMonitor.record(`${operationName}-命中会话缓存`, { 数量: sessionCached.length })
        areaCache.set(key, sessionCached)
        if (id && type === 'city') setDistricts(sessionCached)
        else if (id && type === 'region') setCities(sessionCached)
        else setProvinces(sessionCached)
        return
      }

      // 在开始获取数据时立即清空对应的数据数组，避免显示旧数据
      if (id && type === 'city') {
        // 获取区县数据时，清空区县列表
        setDistricts([])
      } else if (id && type === 'region') {
        // 获取城市数据时，清空城市和区县列表
        setCities([])
        setDistricts([])
      }

      performanceMonitor.start(operationName, {
        类型: type,
        过滤ID: id || '无',
        请求时间: new Date().toISOString(),
      })

      try {
        const response = await getAddressData({ FilterValue: id, type }).unwrap()
        const parsedData = JSON.parse(response.province_city_county_search || '[]') as ApiAreaItem[]

        performanceMonitor.start('地址选择器-数据格式化')
        // 根据类型和是否有id参数来判断数据用途
        const isCity = Boolean(id && type === 'region')
        const formattedData = formatApiData(parsedData, type, isCity)
        performanceMonitor.end('地址选择器-数据格式化', {
          原始数据长度: parsedData.length,
          格式化后长度: formattedData.length,
        })

        // 写入缓存
        areaCache.set(key, formattedData)
        writeAreaToSession(type, id, formattedData)

        // 根据类型设置对应的数据
        if (id && type === 'city') {
          setDistricts(formattedData)
        } else if (id && type === 'region') {
          setCities(formattedData)
        } else {
          setProvinces(formattedData)
        }

        performanceMonitor.end(operationName, {
          成功: true,
          数据数量: formattedData.length,
        })
      } catch (error) {
        performanceMonitor.end(operationName, {
          成功: false,
          错误信息: String(error),
        })
        console.error('获取地址数据失败:', error)
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [getAddressData, formatApiData, toast],
  )

  /**
   * 设置默认值
   */
  const setDefaultValue = useCallback(
    (list: AreaItem[], value: string | undefined, setter: (item: AreaItem) => void) => {
      if (list.length > 0 && value) {
        const defaultItem = list.find((item) => item.label === value)
        if (defaultItem) {
          setter(defaultItem)
        }
      }
    },
    [],
  )

  // ==================== 事件处理函数 ====================

  /**
   * 处理地址选择
   */
  const handleAddressSelect = useCallback(
    (address: AreaItem) => {
      performanceMonitor.start('地址选择器-地址选择交互')

      performanceMonitor.record('地址选择器-地址被选中', {
        当前Tab: activeTab,
        选中地址: address.label,
        地址ID: address.id,
      })

      setIsDefaultSet(false)
      switch (activeTab) {
        case TabType.PROVINCE:
          setActiveProvince(address)
          setActiveCity(DEFAULT_AREA_ITEM)
          setActiveDistrict(DEFAULT_AREA_ITEM)
          setActiveTab(TabType.CITY)
          performanceMonitor.record('地址选择器-切换到城市选择', {
            选中省份: address.label,
          })
          break
        case TabType.CITY:
          setActiveCity(address)
          setActiveDistrict(DEFAULT_AREA_ITEM)
          setActiveTab(TabType.DISTRICT)
          performanceMonitor.record('地址选择器-切换到区县选择', {
            选中城市: address.label,
          })
          break
        case TabType.DISTRICT:
          setActiveDistrict(address)
          performanceMonitor.record('地址选择器-区县选择完成', {
            选中区县: address.label,
          })
          break
      }

      performanceMonitor.end('地址选择器-地址选择交互')
    },
    [activeTab],
  )

  // ==================== 副作用 ====================

  /**
   * 初始化省份数据
   */
  useEffect(() => {
    fetchAddressData({ type: 'region' })
  }, [fetchAddressData])

  /**
   * 监听省份变化，获取城市数据
   */
  useEffect(() => {
    if (activeProvince.id) {
      fetchAddressData({ id: activeProvince.id, type: 'region' })
    }
  }, [fetchAddressData, activeProvince.id])

  /**
   * 监听城市变化，获取区县数据
   */
  useEffect(() => {
    if (activeCity.id) {
      fetchAddressData({ id: activeCity.id, type: 'city' })
    }
  }, [fetchAddressData, activeCity.id])

  /**
   * 设置默认省份
   */
  useEffect(() => {
    setDefaultValue(provinces, defaultValue?.province, setActiveProvince)
  }, [provinces, defaultValue?.province, setDefaultValue])

  /**
   * 设置默认城市
   */
  useEffect(() => {
    setDefaultValue(cities, defaultValue?.city, setActiveCity)
  }, [cities, defaultValue?.city, setDefaultValue])

  /**
   * 设置默认区县
   */
  useEffect(() => {
    setDefaultValue(districts, defaultValue?.district, setActiveDistrict)
  }, [districts, defaultValue?.district, setDefaultValue])

  /**
   * 根据已选择的地址自动设置tab层级
   */
  useEffect(() => {
    if (!initialTab && defaultValue) {
      // 如果没有指定initialTab，根据已选择的地址自动设置
      if (defaultValue.district) {
        setActiveTab(TabType.DISTRICT)
      } else if (defaultValue.city) {
        setActiveTab(TabType.CITY)
      } else if (defaultValue.province) {
        setActiveTab(TabType.PROVINCE)
      }
    }
  }, [initialTab, defaultValue])

  /**
   * 执行选择回调
   */
  useEffect(() => {
    if (onSelect && !isDefaultSet && activeProvince.id) {
      onSelect(activeProvince, activeCity, activeDistrict)
    }
  }, [onSelect, isDefaultSet, activeProvince, activeCity, activeDistrict])

  // ==================== 渲染函数 ====================

  /**
   * 渲染地址列表项
   */
  const renderAddressItem = useCallback(
    (item: AreaItem) => (
      <button
        key={item.id}
        role="option"
        aria-selected={item.id === currentAddress.id}
        tabIndex={item.id === currentAddress.id ? 0 : -1}
        disabled={isFetching}
        className={clsx(
          'w-full cursor-pointer px-4 py-3 text-left hover:bg-gray-50',
          item.id === currentAddress.id && 'text-primary',
          isFetching && 'pointer-events-none',
        )}
        onClick={() => !isFetching && handleAddressSelect(item)}
        onKeyDown={(e) => {
          if (!isFetching && (e.key === 'Enter' || e.key === ' ')) {
            handleAddressSelect(item)
          }
        }}>
        {item.label}
      </button>
    ),
    [currentAddress.id, handleAddressSelect, isFetching],
  )

  /**
   * 渲染地址列表内容
   */
  const renderAddressListContent = useCallback(
    (ariaLabel: string) => (
      <div className="max-h-[320px] overflow-y-auto" role="listbox" aria-label={ariaLabel}>
        {addressList.length > 0
          ? addressList.map(renderAddressItem)
          : isFetching && <LoadingSkeleton />}
      </div>
    ),
    [addressList, isFetching, renderAddressItem],
  )

  // ==================== Tabs配置 ====================

  const items: TabsProps['items'] = useMemo(
    () => [
      {
        key: TabType.PROVINCE,
        label: getI18nString('province'),
        children: renderAddressListContent('选择省份'),
      },
      {
        key: TabType.CITY,
        label: getI18nString('city'),
        children: renderAddressListContent('选择城市'),
      },
      {
        key: TabType.DISTRICT,
        label: getI18nString('district'),
        children: renderAddressListContent('选择区县'),
      },
    ],
    [getI18nString, renderAddressListContent],
  )

  // ==================== 组件返回 ====================

  return (
    <Tabs
      activeKey={activeTab}
      items={items}
      onChange={(key) => setActiveTab(key as TabType)}
      className="region-selector-tabs h-full"
    />
  )
}

export default AddressSelector
