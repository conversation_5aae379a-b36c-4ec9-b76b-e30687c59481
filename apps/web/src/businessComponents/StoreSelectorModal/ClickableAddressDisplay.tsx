'use client'

import { useCallback } from 'react'
import clsx from 'clsx'

// ==================== 类型定义 ====================

/**
 * 地址项接口
 */
interface AreaItem {
  label: string
  id: string
}

/**
 * Tab类型枚举
 */
enum TabType {
  PROVINCE = 'province',
  CITY = 'city',
  DISTRICT = 'district',
}

/**
 * 组件属性接口
 */
interface ClickableAddressDisplayProps {
  province?: AreaItem
  city?: AreaItem
  district?: AreaItem
  onTabChange?: (tab: TabType) => void
  className?: string
}

// ==================== 主组件 ====================

/**
 * 可点击地址显示组件
 *
 * 功能：
 * 1. 显示当前选择的省市区地址
 * 2. 支持点击地址不同部分跳转到对应层级
 * 3. 提供友好的交互反馈
 */
const ClickableAddressDisplay = ({
  province,
  city,
  district,
  onTabChange,
  className,
}: ClickableAddressDisplayProps) => {
  // ==================== 事件处理函数 ====================

  /**
   * 处理省份点击
   */
  const handleProvinceClick = useCallback(() => {
    onTabChange?.(TabType.PROVINCE)
  }, [onTabChange])

  /**
   * 处理城市点击
   */
  const handleCityClick = useCallback(() => {
    if (city?.id) {
      onTabChange?.(TabType.CITY)
    }
  }, [city?.id, onTabChange])

  /**
   * 处理区县点击
   */
  const handleDistrictClick = useCallback(() => {
    if (district?.id) {
      onTabChange?.(TabType.DISTRICT)
    }
  }, [district?.id, onTabChange])

  // ==================== 渲染函数 ====================

  /**
   * 渲染地址部分
   */
  const renderAddressPart = useCallback(
    (
      label: string,
      onClick: () => void,
      isActive: boolean = false,
      isClickable: boolean = true,
    ) => (
      <button
        type="button"
        onClick={isClickable ? onClick : undefined}
        disabled={!isClickable}
        className={clsx(
          'inline-flex items-center px-2 py-1 text-sm transition-colors',
          isClickable && 'cursor-pointer hover:bg-gray-100',
          !isClickable && 'cursor-default',
          isActive && 'font-medium text-primary',
          !isActive && 'text-gray-700',
        )}>
        {label}
      </button>
    ),
    [],
  )

  /**
   * 渲染分隔符
   */
  const renderSeparator = useCallback(() => <span className="mx-1 text-gray-400">/</span>, [])

  // ==================== 条件渲染 ====================

  // 如果没有任何地址信息，显示占位符
  if (!province?.label && !city?.label && !district?.label) {
    return <div className={clsx('flex items-center text-gray-400', className)}>请选择地址</div>
  }

  return (
    <div className={clsx('flex flex-wrap items-center', className)}>
      {/* 省份 */}
      {province?.label && (
        <>
          {renderAddressPart(province.label, handleProvinceClick, false, true)}
          {(city?.label || district?.label) && renderSeparator()}
        </>
      )}

      {/* 城市 */}
      {city?.label && (
        <>
          {renderAddressPart(city.label, handleCityClick, false, !!city.id)}
          {district?.label && renderSeparator()}
        </>
      )}

      {/* 区县 */}
      {district?.label && (
        <>{renderAddressPart(district.label, handleDistrictClick, false, !!district.id)}</>
      )}
    </div>
  )
}

export default ClickableAddressDisplay
export type { AreaItem, ClickableAddressDisplayProps, TabType }
