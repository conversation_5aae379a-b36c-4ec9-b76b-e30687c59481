'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  resolveCatchMessage,
  TCatchMessage,
  useLazyGetProvinceCityCountyQuery,
  useToastContext,
} from '@ninebot/core'
import { Tabs } from 'antd-mobile'

interface AreaItem {
  label: string
  id: string
}

// 接口返回数据类型
interface ApiAreaItem {
  default_name: string
  region_id?: string
  city_id?: string
  district_id?: string
}

interface AddressCascaderProps {
  defaultValue?: {
    province?: string
    city?: string
    district?: string
  }
  onSelect?: (province: AreaItem, city: AreaItem, district: AreaItem) => void
  initialTab?: 'province' | 'city' | 'district' // 初始显示的tab层级
}

const DEFAULT_VALUE = { label: '', id: '' }

const AddressCascader = ({ defaultValue, onSelect, initialTab }: AddressCascaderProps) => {
  const getI18nString = useTranslations('Common')
  const [getAddressData, { isFetching }] = useLazyGetProvinceCityCountyQuery()
  const toast = useToastContext()

  // 定义Tab数据
  const TABS = [
    { key: 'province', title: getI18nString('province') },
    { key: 'city', title: getI18nString('city') },
    { key: 'district', title: getI18nString('district') },
  ]

  const [activeTab, setActiveTab] = useState(initialTab || 'province')

  const [activeProvince, setActiveProvince] = useState<AreaItem>(DEFAULT_VALUE)
  const [activeCity, setActiveCity] = useState<AreaItem>(DEFAULT_VALUE)
  const [activeDistrict, setActiveDistrict] = useState<AreaItem>(DEFAULT_VALUE)

  const [provinces, setProvinces] = useState<AreaItem[]>([])
  const [cities, setCities] = useState<AreaItem[]>([])
  const [districts, setDistricts] = useState<AreaItem[]>([])

  const [isDefaultSet, setIsDefaultSet] = useState(true) // 是否设置默认值

  /**
   * 获取地址数据
   */
  const fetchAddressData = useCallback(
    ({ id, type }: { id?: string; type: string }) => {
      getAddressData({ FilterValue: id, type })
        .unwrap()
        .then((res) => {
          if (!res?.province_city_county_search) return
          const parsedData = JSON.parse(res.province_city_county_search) as ApiAreaItem[]
          if (id && type === 'city') {
            const formatData = parsedData.map((item) => ({
              id: item.district_id || '',
              label: item.default_name,
            }))
            setDistricts(formatData)
          } else if (id && type === 'region') {
            const formatData = parsedData.map((item) => ({
              id: item.city_id || '',
              label: item.default_name,
            }))
            setCities(formatData)
          } else {
            const formatData = parsedData.map((item) => ({
              id: item.region_id || '',
              label: item.default_name,
            }))
            setProvinces(formatData)
          }
        })
        .catch((error) => {
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error as TCatchMessage) as string,
          })
        })
    },
    [getAddressData, toast],
  )

  /**
   * 获取省份
   */
  useEffect(() => {
    fetchAddressData({ type: 'region' })
  }, [fetchAddressData])

  /**
   * 获取城市
   */
  useEffect(() => {
    if (activeProvince.id) {
      fetchAddressData({ id: activeProvince.id, type: 'region' })
    }
  }, [fetchAddressData, activeProvince.id])

  /**
   * 获取区
   */
  useEffect(() => {
    if (activeCity.id) {
      fetchAddressData({ id: activeCity.id, type: 'city' })
    }
  }, [fetchAddressData, activeCity.id])

  /**
   * 设置默认省份
   */
  useEffect(() => {
    if (provinces.length > 0 && defaultValue?.province) {
      const defaultProvince = provinces.find((province) => province.label === defaultValue.province)
      if (defaultProvince) {
        setActiveProvince(defaultProvince)
      }
    }
  }, [provinces, defaultValue?.province])

  /**
   * 设置默认城市
   */
  useEffect(() => {
    if (cities.length > 0 && defaultValue?.city) {
      const defaultCity = cities.find((city) => city.label === defaultValue.city)
      if (defaultCity) {
        setActiveCity(defaultCity)
      }
    }
  }, [cities, defaultValue?.city])

  /**
   * 设置默认区
   */
  useEffect(() => {
    if (districts.length > 0 && defaultValue?.district) {
      const defaultDistrict = districts.find((district) => district.label === defaultValue.district)
      if (defaultDistrict) {
        setActiveDistrict(defaultDistrict)
      }
    }
  }, [districts, defaultValue?.district])

  /**
   * 根据已选择的地址自动设置tab层级
   */
  useEffect(() => {
    if (!initialTab && defaultValue) {
      // 如果没有指定initialTab，根据已选择的地址自动设置
      if (defaultValue.district) {
        setActiveTab('district')
      } else if (defaultValue.city) {
        setActiveTab('city')
      } else if (defaultValue.province) {
        setActiveTab('province')
      }
    }
  }, [initialTab, defaultValue])

  /**
   * 地址列表
   */
  const addressList = useMemo(() => {
    switch (activeTab) {
      case 'province':
        return provinces
      case 'city':
        return cities
      case 'district':
        return districts
      default:
        return []
    }
  }, [activeTab, provinces, cities, districts])

  /**
   * 当前选中的item
   */
  const currentAddress = useMemo(() => {
    switch (activeTab) {
      case 'province':
        return activeProvince
      case 'city':
        return activeCity
      case 'district':
        return activeDistrict
      default:
        return DEFAULT_VALUE
    }
  }, [activeTab, activeProvince, activeCity, activeDistrict])

  /**
   * 选择地址
   */
  const handleAddressSelect = (address: AreaItem) => {
    setIsDefaultSet(false)
    switch (activeTab) {
      case 'province':
        setActiveProvince(address)
        setActiveCity(DEFAULT_VALUE)
        setActiveDistrict(DEFAULT_VALUE)
        setActiveTab('city')
        break
      case 'city':
        setActiveCity(address)
        setActiveDistrict(DEFAULT_VALUE)
        setActiveTab('district')
        break
      case 'district':
        setActiveDistrict(address)
        break
    }
  }

  /**
   * 执行props回调
   */
  useEffect(() => {
    if (onSelect && !isDefaultSet && activeProvince.id) {
      onSelect(activeProvince, activeCity, activeDistrict)
    }
  }, [onSelect, isDefaultSet, activeProvince, activeCity, activeDistrict])

  return (
    <div className="address-selector">
      <Tabs className="items-left" activeKey={activeTab} onChange={setActiveTab}>
        {TABS.map((tab) => (
          <Tabs.Tab className="address-select-tab" key={tab.key} title={tab.title} />
        ))}
      </Tabs>

      <div className="address-list">
        {addressList.length > 0 ? (
          <div className="list-container">
            {addressList.map((item) => (
              <div
                key={item.id}
                className={`address-item ${item.id === currentAddress.id ? 'active' : ''}`}
                onClick={() => handleAddressSelect(item)}>
                {item.label}
              </div>
            ))}
          </div>
        ) : (
          <div className="loading-container">
            {isFetching && (
              <div className="skeleton-list">
                {Array(9)
                  .fill(null)
                  .map((_, index) => (
                    <div key={index} className="skeleton-item" />
                  ))}
              </div>
            )}
          </div>
        )}
      </div>

      <style jsx>{`
        .address-selector {
          height: 380px;
        }
        .address-list {
          overflow-y: auto;
          padding: 0;
          height: 300px;
        }
        .list-container {
          padding: 8px 0;
        }
        .address-item {
          padding: 12px 0;
          font-size: 14px;
        }
        .address-item.active {
          color: #da291c;
        }
        .loading-container {
          height: 405px;
          overflow: hidden;
        }
        .skeleton-list {
          padding: 8px 0;
        }
        .skeleton-item {
          height: 22px;
          margin-top: 24px;
          background: #f5f5f5;
          border-radius: 4px;
        }
      `}</style>
    </div>
  )
}

export default AddressCascader
